-- SQLite 数据库结构文件
-- 注意：使用SQLite时，此文件仅供参考，表结构会在程序启动时自动创建

-- 创建序列号表
CREATE TABLE IF NOT EXISTS serial_numbers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    serial_number TEXT NOT NULL UNIQUE,
    batch_id TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建生成任务表
CREATE TABLE IF NOT EXISTS generation_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    batch_id TEXT NOT NULL UNIQUE,
    total_count INTEGER NOT NULL,
    generated_count INTEGER DEFAULT 0,
    status INTEGER DEFAULT 0, -- 0-进行中，1-已完成，2-失败
    error_message TEXT,
    started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_batch_id ON serial_numbers(batch_id);
CREATE INDEX IF NOT EXISTS idx_created_at ON serial_numbers(created_at);
CREATE INDEX IF NOT EXISTS idx_serial_number_prefix ON serial_numbers(substr(serial_number, 1, 4));
CREATE INDEX IF NOT EXISTS idx_status ON generation_tasks(status);

-- 创建更新触发器（模拟MySQL的ON UPDATE CURRENT_TIMESTAMP）
CREATE TRIGGER IF NOT EXISTS update_serial_numbers_timestamp 
AFTER UPDATE ON serial_numbers
BEGIN
    UPDATE serial_numbers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END; 