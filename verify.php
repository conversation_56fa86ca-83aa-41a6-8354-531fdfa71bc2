<?php
/**
 * 序列号验证页面
 * 传统PHP页面，用于验证用户输入的序列号是否存在于数据库中
 */

// 引入配置和检查类
require_once 'config.php';

// 序列号检查器类（从check_serial.php复制）
class SerialVerifier {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * 验证序列号
     * @param string $serial 要验证的序列号
     * @return array 验证结果
     */
    public function verifySerial($serial) {
        try {
            // 验证序列号格式
            $validation = $this->validateSerial($serial);
            if (!$validation['valid']) {
                return [
                    'success' => false,
                    'message' => $validation['message'],
                    'type' => 'format_error',
                    'serial' => $serial
                ];
            }
            
            // 查询数据库
            $pdo = $this->db->getConnection();
            $sql = "SELECT serial_number, batch_id, created_at, first_queried_at, query_count FROM serial_numbers WHERE serial_number = ? LIMIT 1";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$serial]);
            $result = $stmt->fetch();

            if ($result) {
                // 更新查询记录
                $this->db->updateQueryRecord($serial);

                // 获取更新后的查询统计
                $queryStats = $this->db->getQueryStats($serial);
                $queryCount = $queryStats ? $queryStats['query_count'] : ($result['query_count'] + 1);
                $firstQueriedAt = $queryStats ? $queryStats['first_queried_at'] : null;

                // 如果是首次查询，使用当前时间
                if (!$firstQueriedAt) {
                    $firstQueriedAt = date('Y-m-d H:i:s');
                }

                // 序列号存在
                return [
                    'success' => true,
                    'message' => '序列号验证成功！该序列号存在于数据库中。',
                    'type' => 'success',
                    'serial' => $result['serial_number'],
                    'batch_id' => $result['batch_id'],
                    'created_at' => $result['created_at'],
                    'first_queried_at' => $firstQueriedAt,
                    'query_count' => $queryCount,
                    'database_type' => $this->db->getDbType()
                ];
            } else {
                // 序列号不存在
                return [
                    'success' => false,
                    'message' => '序列号验证失败！该序列号不存在于数据库中。',
                    'type' => 'not_found',
                    'serial' => $serial,
                    'database_type' => $this->db->getDbType()
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '验证过程中发生错误：' . $e->getMessage(),
                'type' => 'error',
                'serial' => $serial
            ];
        }
    }
    
    /**
     * 验证序列号格式
     * @param string $serial 序列号
     * @return array 验证结果
     */
    private function validateSerial($serial) {
        // 检查是否为空
        if (empty($serial)) {
            return [
                'valid' => false,
                'message' => '请输入序列号'
            ];
        }
        
        // 去除空格
        $serial = trim($serial);
        
        // 检查长度
        if (strlen($serial) != 16) {
            return [
                'valid' => false,
                'message' => '序列号长度必须为16位'
            ];
        }
        
        // 检查是否为数字
        if (!is_numeric($serial)) {
            return [
                'valid' => false,
                'message' => '序列号必须为纯数字'
            ];
        }
        
        // 检查前4位是否为0000
        if (substr($serial, 0, 4) === '0000') {
            return [
                'valid' => false,
                'message' => '序列号前4位不能为0000'
            ];
        }
        
        return [
            'valid' => true,
            'message' => '序列号格式正确'
        ];
    }
}

// 处理表单提交
$result = null;
$inputSerial = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['serial'])) {
    $inputSerial = trim($_POST['serial']);
    $verifier = new SerialVerifier();
    $result = $verifier->verifySerial($inputSerial);
    
    // 记录验证日志
    $logMessage = date('Y-m-d H:i:s') . " - 页面验证: serial={$inputSerial}, result=" . ($result['success'] ? 'success' : 'failed') . ", type={$result['type']}\n";
    file_put_contents('verify.log', $logMessage, FILE_APPEND | LOCK_EX);
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>序列号验证系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
            font-size: 1.1em;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1.1em;
            font-family: 'Courier New', monospace;
            letter-spacing: 1px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid;
            font-size: 1.1em;
            line-height: 1.6;
        }

        .result.success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .result.error {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        .result.format-error {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }

        .result-details {
            margin-top: 15px;
            background: rgba(255, 255, 255, 0.3);
            padding: 15px;
            border-radius: 5px;
            font-size: 0.95em;
        }

        .result-details strong {
            display: inline-block;
            width: 100px;
            margin-right: 10px;
        }

        .result-details div {
            margin: 5px 0;
        }

        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            color: #1565c0;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .info-box h4 {
            margin-bottom: 8px;
            color: #0d47a1;
        }

        .info-box ul {
            margin-left: 20px;
        }

        .info-box li {
            margin: 3px 0;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 0.9em;
        }

        .footer a {
            color: #667eea;
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 序列号验证系统</h1>
            <p>验证16位序列号是否存在于数据库中</p>
        </div>

        <div class="info-box">
            <h4>📋 验证说明：</h4>
            <ul>
                <li>序列号必须为16位纯数字</li>
                <li>前4位不能为0000</li>
                <li>系统将检查序列号是否存在于数据库中</li>
                <li>当前数据库类型：<?php 
                    try {
                        $db = new Database();
                        echo strtoupper($db->getDbType());
                    } catch (Exception $e) {
                        echo '未知';
                    }
                ?></li>
            </ul>
        </div>

        <div class="form-section">
            <form method="POST" action="">
                <div class="form-group">
                    <label for="serial">输入序列号：</label>
                    <input 
                        type="text" 
                        id="serial" 
                        name="serial" 
                        value="<?php echo htmlspecialchars($inputSerial); ?>"
                        placeholder="请输入16位数字序列号，例如：1234567890123456"
                        maxlength="16"
                        autocomplete="off"
                        required
                    >
                </div>
                
                <button type="submit" class="btn">🔍 验证序列号</button>
            </form>
        </div>

        <?php if ($result): ?>
        <div class="result <?php 
            if ($result['success']) {
                echo 'success';
            } elseif ($result['type'] === 'format_error') {
                echo 'format-error';
            } else {
                echo 'error';
            }
        ?>">
            <strong>
                <?php if ($result['success']): ?>
                    ✅ 验证成功
                <?php else: ?>
                    ❌ 验证失败
                <?php endif; ?>
            </strong>
            <div><?php echo htmlspecialchars($result['message']); ?></div>
            
            <?php if ($result['success']): ?>
            <div class="result-details">
                <div><strong>序列号：</strong><?php echo htmlspecialchars($result['serial']); ?></div>
                <div><strong>批次ID：</strong><?php echo htmlspecialchars($result['batch_id']); ?></div>
                <div><strong>创建时间：</strong><?php echo htmlspecialchars($result['created_at']); ?></div>
                <?php if (isset($result['first_queried_at']) && $result['first_queried_at']): ?>
                <div><strong>首次查询：</strong><?php echo htmlspecialchars($result['first_queried_at']); ?></div>
                <?php endif; ?>
                <?php if (isset($result['query_count'])): ?>
                <div><strong>查询次数：</strong><?php echo number_format($result['query_count']); ?> 次</div>
                <?php endif; ?>
                <div><strong>数据库：</strong><?php echo strtoupper($result['database_type']); ?></div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <div class="footer">
            <p>
                <a href="index.html">🏠 返回主页</a> |
                <a href="check_serial.php?action=stats">📊 查看统计</a> |
                <a href="API_Documentation.md">📖 API文档</a>
            </p>
            <p style="margin-top: 10px;">
                序列号生成器系统 © 2024
            </p>
        </div>
    </div>

    <script>
        // 自动格式化输入
        document.getElementById('serial').addEventListener('input', function(e) {
            // 只允许数字
            this.value = this.value.replace(/[^0-9]/g, '');
            
            // 限制最大长度
            if (this.value.length > 16) {
                this.value = this.value.substring(0, 16);
            }
        });

        // 表单提交验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const serial = document.getElementById('serial').value.trim();
            
            if (serial.length === 0) {
                alert('请输入序列号！');
                e.preventDefault();
                return;
            }
            
            if (serial.length !== 16) {
                alert('序列号必须为16位数字！');
                e.preventDefault();
                return;
            }
            
            if (!/^\d+$/.test(serial)) {
                alert('序列号只能包含数字！');
                e.preventDefault();
                return;
            }
            
            if (serial.substring(0, 4) === '0000') {
                alert('序列号前4位不能为0000！');
                e.preventDefault();
                return;
            }
        });

        // 自动选中输入框内容（便于重新输入）
        <?php if ($result): ?>
        document.getElementById('serial').select();
        <?php endif; ?>
    </script>
</body>
</html> 