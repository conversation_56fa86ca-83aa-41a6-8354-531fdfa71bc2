-- 创建数据库
CREATE DATABASE IF NOT EXISTS `serial_generator` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `serial_generator`;

-- 创建序列号表
CREATE TABLE IF NOT EXISTS `serial_numbers` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `serial_number` varchar(16) NOT NULL COMMENT '16位序列号',
    `batch_id` varchar(32) NOT NULL COMMENT '批次ID',
    `first_queried_at` timestamp NULL DEFAULT NULL COMMENT '首次查询时间',
    `query_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '查询次数',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_serial_number` (`serial_number`),
    KEY `idx_batch_id` (`batch_id`),
    <PERSON><PERSON>Y `idx_created_at` (`created_at`),
    KEY `idx_first_queried_at` (`first_queried_at`),
    KEY `idx_query_count` (`query_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='序列号表';

-- 创建生成任务表
CREATE TABLE IF NOT EXISTS `generation_tasks` (
    `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `batch_id` varchar(32) NOT NULL COMMENT '批次ID',
    `total_count` int(11) NOT NULL COMMENT '总生成数量',
    `generated_count` int(11) DEFAULT 0 COMMENT '已生成数量',
    `status` tinyint(1) DEFAULT 0 COMMENT '状态：0-进行中，1-已完成，2-失败',
    `error_message` text COMMENT '错误信息',
    `started_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `completed_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_batch_id` (`batch_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生成任务表';

-- 创建索引以提高查询性能
CREATE INDEX idx_serial_number_prefix ON serial_numbers(serial_number(4)); 