<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 设置PHP执行时间限制
set_time_limit(0);
ini_set('memory_limit', '512M');

require_once 'generator.php';

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => '只支持POST请求'
    ]);
    exit;
}

// 获取POST参数
$count = isset($_POST['count']) ? intval($_POST['count']) : 0;

// 参数验证
if ($count <= 0 || $count > 1000) {
    echo json_encode([
        'success' => false,
        'message' => '生成数量必须在1-1000万之间'
    ]);
    exit;
}

try {
    // 记录开始时间
    $startTime = microtime(true);
    
    // 创建生成器实例
    $generator = new SerialGenerator();
    
    // 生成序列号
    $result = $generator->generateSerials($count);
    
    // 记录结束时间
    $endTime = microtime(true);
    $executionTime = round($endTime - $startTime, 2);
    
    // 添加执行时间到结果中
    $result['execution_time'] = $executionTime;
    
    // 如果生成成功，获取统计信息
    if ($result['success']) {
        $stats = $generator->getStatistics();
        $result['total_in_database'] = $stats['total_serials'];
        
        // 记录日志
        $logMessage = date('Y-m-d H:i:s') . " - 成功生成 {$result['generated_count']} 个序列号，耗时 {$executionTime} 秒\n";
        file_put_contents('generation.log', $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    // 记录错误日志
    $errorMessage = date('Y-m-d H:i:s') . " - 生成失败: " . $e->getMessage() . "\n";
    file_put_contents('error.log', $errorMessage, FILE_APPEND | LOCK_EX);
    
    echo json_encode([
        'success' => false,
        'message' => '生成过程中发生错误: ' . $e->getMessage()
    ]);
}
?> 