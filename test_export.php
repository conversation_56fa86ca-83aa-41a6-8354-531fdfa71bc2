<?php
/**
 * 导出功能测试脚本
 */

require_once 'export.php';

echo "🧪 开始测试序列号导出功能...\n\n";

// 创建导出器实例
$exporter = new SerialExporter();

// 1. 获取数据库统计信息
echo "📊 获取数据库统计信息:\n";
$stats = $exporter->getStatistics();
echo "- 总记录数: " . number_format($stats['total']) . "\n";
echo "- 最小ID: " . $stats['min_id'] . "\n";
echo "- 最大ID: " . $stats['max_id'] . "\n";
echo "- 数据库类型: " . strtoupper($stats['database_type']) . "\n\n";

if ($stats['total'] == 0) {
    echo "❌ 数据库中没有序列号记录，请先生成一些序列号再测试导出功能。\n";
    exit(1);
}

// 2. 测试导出前100条记录
echo "📤 测试导出前100条记录...\n";
$startId = $stats['min_id'];
$endId = min($startId + 99, $stats['max_id']); // 最多100条

$result = $exporter->exportToCSV($startId, $endId);

if ($result['success']) {
    echo "✅ 导出成功!\n";
    echo "- 文件名: " . $result['filename'] . "\n";
    echo "- 导出数量: " . number_format($result['count']) . " 条\n";
    echo "- ID范围: " . $result['range'] . "\n";
    echo "- 文件大小: " . $result['size'] . "\n";
    echo "- 保存路径: " . $result['filepath'] . "\n\n";
    
    // 验证文件是否真的存在
    if (file_exists($result['filepath'])) {
        echo "✅ CSV文件已成功创建\n";
        
        // 读取文件前几行验证内容
        $file = fopen($result['filepath'], 'r');
        if ($file) {
            echo "\n📄 CSV文件内容预览:\n";
            $lineCount = 0;
            while (($line = fgets($file)) !== false && $lineCount < 5) {
                echo "  " . trim($line) . "\n";
                $lineCount++;
            }
            fclose($file);
            
            if ($lineCount > 1) {
                echo "  ... (共 " . $result['count'] . " 条数据记录)\n";
            }
        }
    } else {
        echo "❌ 文件创建失败\n";
    }
} else {
    echo "❌ 导出失败: " . $result['message'] . "\n";
}

echo "\n";

// 3. 测试错误情况
echo "🔧 测试错误处理...\n";

// 测试无效ID范围
echo "- 测试无效ID范围 (起始ID > 截止ID): ";
$errorResult = $exporter->exportToCSV(100, 50);
if (!$errorResult['success']) {
    echo "✅ 正确捕获错误: " . $errorResult['message'] . "\n";
} else {
    echo "❌ 应该返回错误但没有\n";
}

// 测试超出范围的ID
echo "- 测试超出数据库范围的ID: ";
$maxId = $stats['max_id'];
$errorResult = $exporter->exportToCSV($maxId + 1000, $maxId + 2000);
if (!$errorResult['success']) {
    echo "✅ 正确捕获错误: " . $errorResult['message'] . "\n";
} else {
    echo "❌ 应该返回错误但没有\n";
}

echo "\n";

// 4. 检查日志文件
echo "📝 检查导出日志:\n";
if (file_exists('export.log')) {
    echo "✅ 导出日志文件存在\n";
    $logContent = file_get_contents('export.log');
    $lines = explode("\n", trim($logContent));
    $recentLines = array_slice($lines, -3); // 最后3行
    
    echo "最近的日志记录:\n";
    foreach ($recentLines as $line) {
        if (!empty($line)) {
            echo "  " . $line . "\n";
        }
    }
} else {
    echo "⚠️ 导出日志文件不存在\n";
}

echo "\n";

// 5. 检查CSV文件夹
echo "📁 检查CSV文件夹:\n";
$csvDir = __DIR__ . '/csv';
if (is_dir($csvDir)) {
    echo "✅ CSV文件夹存在\n";
    $files = glob($csvDir . '/*.csv');
    echo "CSV文件数量: " . count($files) . "\n";
    
    if (count($files) > 0) {
        echo "最新的CSV文件:\n";
        foreach (array_slice($files, -3) as $file) {
            $filename = basename($file);
            $size = filesize($file);
            $time = date('Y-m-d H:i:s', filemtime($file));
            echo "  - {$filename} ({$size} bytes, {$time})\n";
        }
    }
} else {
    echo "❌ CSV文件夹不存在\n";
}

echo "\n�� 导出功能测试完成!\n";
?> 