<?php
/**
 * 序列号批量导出系统
 * 支持按ID范围导出序列号为CSV文件
 */

// 引入配置文件
require_once 'config.php';

// CSV导出器类
class SerialExporter {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * 导出序列号为CSV文件
     * @param int $startId 起始ID（包含）
     * @param int $endId 截止ID（包含）
     * @return array 导出结果
     */
    public function exportToCSV($startId, $endId) {
        try {
            // 验证参数
            if ($startId <= 0 || $endId <= 0) {
                return [
                    'success' => false,
                    'message' => 'ID必须为正整数'
                ];
            }
            
            if ($startId > $endId) {
                return [
                    'success' => false,
                    'message' => '起始ID不能大于截止ID'
                ];
            }
            
            // 检查ID范围是否过大（防止内存溢出）
            $maxRange = 100000; // 最大导出10万条
            if (($endId - $startId + 1) > $maxRange) {
                return [
                    'success' => false,
                    'message' => "导出范围过大，最多支持导出 {$maxRange} 条记录"
                ];
            }
            
            // 查询数据库
            $pdo = $this->db->getConnection();
            $sql = "SELECT id, serial_number, batch_id, created_at, updated_at 
                    FROM serial_numbers 
                    WHERE id >= ? AND id <= ? 
                    ORDER BY id ASC";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$startId, $endId]);
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($results)) {
                return [
                    'success' => false,
                    'message' => '指定ID范围内没有找到任何序列号'
                ];
            }
            
            // 创建CSV文件夹（如果不存在）
            $csvDir = __DIR__ . '/csv';
            if (!is_dir($csvDir)) {
                if (!mkdir($csvDir, 0755, true)) {
                    return [
                        'success' => false,
                        'message' => '无法创建CSV文件夹'
                    ];
                }
            }
            
            // 生成文件名：日期_数量.csv
            $date = date('Ymd');
            $count = count($results);
            $filename = "{$date}_{$count}.csv";
            $filepath = $csvDir . '/' . $filename;
            
            // 如果文件已存在，添加时间戳避免冲突
            if (file_exists($filepath)) {
                $timestamp = date('His');
                $filename = "{$date}_{$count}_{$timestamp}.csv";
                $filepath = $csvDir . '/' . $filename;
            }
            
            // 创建CSV文件
            $file = fopen($filepath, 'w');
            if (!$file) {
                return [
                    'success' => false,
                    'message' => '无法创建CSV文件'
                ];
            }
            
            // 设置CSV文件为UTF-8编码（添加BOM以支持Excel正确显示中文）
            fwrite($file, "\xEF\xBB\xBF");
            
            // 写入CSV标题行
            $headers = ['ID', '序列号', '批次ID', '创建时间', '更新时间'];
            fputcsv($file, $headers);
            
            // 写入数据行
            foreach ($results as $row) {
                $csvRow = [
                    $row['id'],
                    $row['serial_number'],
                    $row['batch_id'],
                    $row['created_at'],
                    $row['updated_at']
                ];
                fputcsv($file, $csvRow);
            }
            
            fclose($file);
            
            // 记录导出日志
            $logMessage = date('Y-m-d H:i:s') . " - 导出成功: 范围={$startId}-{$endId}, 数量={$count}, 文件={$filename}\n";
            file_put_contents('export.log', $logMessage, FILE_APPEND | LOCK_EX);
            
            return [
                'success' => true,
                'message' => '导出成功',
                'filename' => $filename,
                'filepath' => $filepath,
                'count' => $count,
                'range' => "{$startId} - {$endId}",
                'size' => $this->formatFileSize(filesize($filepath))
            ];
            
        } catch (Exception $e) {
            // 记录错误日志
            $errorMessage = date('Y-m-d H:i:s') . " - 导出失败: " . $e->getMessage() . "\n";
            file_put_contents('export.log', $errorMessage, FILE_APPEND | LOCK_EX);
            
            return [
                'success' => false,
                'message' => '导出失败：' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取数据库统计信息
     * @return array 统计信息
     */
    public function getStatistics() {
        try {
            $pdo = $this->db->getConnection();
            
            // 获取总记录数
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM serial_numbers");
            $total = $stmt->fetch()['total'];
            
            // 获取ID范围
            $stmt = $pdo->query("SELECT MIN(id) as min_id, MAX(id) as max_id FROM serial_numbers");
            $range = $stmt->fetch();
            
            return [
                'total' => $total,
                'min_id' => $range['min_id'] ?? 0,
                'max_id' => $range['max_id'] ?? 0,
                'database_type' => $this->db->getDbType()
            ];
            
        } catch (Exception $e) {
            return [
                'total' => 0,
                'min_id' => 0,
                'max_id' => 0,
                'database_type' => 'unknown'
            ];
        }
    }
    
    /**
     * 格式化文件大小
     * @param int $size 文件大小（字节）
     * @return string 格式化后的大小
     */
    private function formatFileSize($size) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }
        
        return round($size, 2) . ' ' . $units[$unitIndex];
    }
}

// 处理表单提交
$result = null;
$startId = '';
$endId = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['start_id']) && isset($_POST['end_id'])) {
    $startId = intval($_POST['start_id']);
    $endId = intval($_POST['end_id']);
    
    $exporter = new SerialExporter();
    $result = $exporter->exportToCSV($startId, $endId);
}

// 获取统计信息
$exporter = new SerialExporter();
$stats = $exporter->getStatistics();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>序列号批量导出系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            max-width: 700px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .stats-box {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .stats-box h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .stat-item .value {
            font-size: 1.5em;
            font-weight: bold;
            color: #0984e3;
            margin-bottom: 5px;
        }

        .stat-item .label {
            font-size: 0.9em;
            color: #6c757d;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
            font-size: 1.1em;
        }

        .form-group input {
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1.1em;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #74b9ff;
            box-shadow: 0 0 0 3px rgba(116, 185, 255, 0.1);
        }

        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(116, 185, 255, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid;
            font-size: 1.1em;
            line-height: 1.6;
        }

        .result.success {
            background: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .result.error {
            background: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        .result-details {
            margin-top: 15px;
            background: rgba(255, 255, 255, 0.5);
            padding: 15px;
            border-radius: 5px;
            font-size: 0.95em;
        }

        .result-details .detail-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .result-details .detail-row:last-child {
            border-bottom: none;
        }

        .result-details .label {
            font-weight: 600;
            color: #333;
        }

        .result-details .value {
            color: #666;
            font-family: 'Courier New', monospace;
        }

        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            color: #1565c0;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .info-box h4 {
            margin-bottom: 8px;
            color: #0d47a1;
        }

        .info-box ul {
            margin-left: 20px;
        }

        .info-box li {
            margin: 3px 0;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 0.9em;
        }

        .footer a {
            color: #0984e3;
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📤 序列号批量导出</h1>
            <p>按ID范围导出序列号为CSV文件</p>
        </div>

        <div class="stats-box">
            <h3>📊 数据库统计</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="value"><?php echo number_format($stats['total']); ?></div>
                    <div class="label">总记录数</div>
                </div>
                <div class="stat-item">
                    <div class="value"><?php echo $stats['min_id']; ?></div>
                    <div class="label">最小ID</div>
                </div>
                <div class="stat-item">
                    <div class="value"><?php echo $stats['max_id']; ?></div>
                    <div class="label">最大ID</div>
                </div>
                <div class="stat-item">
                    <div class="value"><?php echo strtoupper($stats['database_type']); ?></div>
                    <div class="label">数据库类型</div>
                </div>
            </div>
        </div>

        <div class="info-box">
            <h4>📋 导出说明：</h4>
            <ul>
                <li>请输入要导出的ID范围（包含起始和截止ID）</li>
                <li>最多支持单次导出100,000条记录</li>
                <li>文件将保存到服务器的csv文件夹中</li>
                <li>文件名格式：日期_数量.csv（如：20240101_1000.csv）</li>
                <li>CSV文件包含：ID、序列号、批次ID、创建时间、更新时间</li>
            </ul>
        </div>

        <div class="form-section">
            <form method="POST" action="">
                <div class="form-row">
                    <div class="form-group">
                        <label for="start_id">起始ID（包含）：</label>
                        <input 
                            type="number" 
                            id="start_id" 
                            name="start_id" 
                            value="<?php echo htmlspecialchars($startId); ?>"
                            min="1"
                            max="<?php echo $stats['max_id']; ?>"
                            placeholder="例如：1"
                            required
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="end_id">截止ID（包含）：</label>
                        <input 
                            type="number" 
                            id="end_id" 
                            name="end_id" 
                            value="<?php echo htmlspecialchars($endId); ?>"
                            min="1"
                            max="<?php echo $stats['max_id']; ?>"
                            placeholder="例如：1000"
                            required
                        >
                    </div>
                </div>
                
                <button type="submit" class="btn">📤 导出CSV文件</button>
            </form>
        </div>

        <?php if ($result): ?>
        <div class="result <?php echo $result['success'] ? 'success' : 'error'; ?>">
            <strong>
                <?php if ($result['success']): ?>
                    ✅ 导出成功
                <?php else: ?>
                    ❌ 导出失败
                <?php endif; ?>
            </strong>
            <div><?php echo htmlspecialchars($result['message']); ?></div>
            
            <?php if ($result['success']): ?>
            <div class="result-details">
                <div class="detail-row">
                    <span class="label">文件名：</span>
                    <span class="value"><?php echo htmlspecialchars($result['filename']); ?></span>
                </div>
                <div class="detail-row">
                    <span class="label">导出数量：</span>
                    <span class="value"><?php echo number_format($result['count']); ?> 条</span>
                </div>
                <div class="detail-row">
                    <span class="label">ID范围：</span>
                    <span class="value"><?php echo htmlspecialchars($result['range']); ?></span>
                </div>
                <div class="detail-row">
                    <span class="label">文件大小：</span>
                    <span class="value"><?php echo htmlspecialchars($result['size']); ?></span>
                </div>
                <div class="detail-row">
                    <span class="label">保存路径：</span>
                    <span class="value">./csv/<?php echo htmlspecialchars($result['filename']); ?></span>
                </div>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <div class="footer">
            <p>
                <a href="index.html">🏠 返回主页</a> |
                <a href="verify.php">🔍 验证序列号</a> |
                <a href="check_serial.php?action=stats">📊 查看统计</a>
            </p>
            <p style="margin-top: 10px;">
                序列号生成器系统 © 2024
            </p>
        </div>
    </div>

    <script>
        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const startId = parseInt(document.getElementById('start_id').value);
            const endId = parseInt(document.getElementById('end_id').value);
            const maxId = <?php echo $stats['max_id']; ?>;
            
            if (startId <= 0 || endId <= 0) {
                alert('ID必须为正整数！');
                e.preventDefault();
                return;
            }
            
            if (startId > endId) {
                alert('起始ID不能大于截止ID！');
                e.preventDefault();
                return;
            }
            
            if (startId > maxId || endId > maxId) {
                alert(`ID不能超过最大值 ${maxId}！`);
                e.preventDefault();
                return;
            }
            
            const count = endId - startId + 1;
            if (count > 100000) {
                alert('单次导出不能超过100,000条记录！');
                e.preventDefault();
                return;
            }
            
            // 确认导出
            if (!confirm(`确定要导出 ${count.toLocaleString()} 条记录吗？`)) {
                e.preventDefault();
                return;
            }
        });

        // 自动设置截止ID
        document.getElementById('start_id').addEventListener('change', function() {
            const startId = parseInt(this.value);
            const endIdField = document.getElementById('end_id');
            
            if (startId && !endIdField.value) {
                // 如果起始ID有值但截止ID没有值，自动设置为起始ID+999（最多1000条）
                const suggestedEndId = Math.min(startId + 999, <?php echo $stats['max_id']; ?>);
                endIdField.value = suggestedEndId;
            }
        });
    </script>
</body>
</html> 