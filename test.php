<?php
/**
 * 序列号生成器测试脚本
 * 用于验证系统功能是否正常
 */

require_once 'generator.php';

echo "🔢 序列号生成器 - 测试脚本\n";
echo "==========================\n\n";

try {
    // 测试数据库连接
    echo "📡 测试数据库连接...\n";
    $generator = new SerialGenerator();
    echo "✅ 数据库连接成功\n\n";
    
    // 获取当前统计信息
    echo "📊 当前数据库统计信息:\n";
    $stats = $generator->getStatistics();
    echo "- 当前总序列号数量: " . number_format($stats['total_serials']) . " 个\n\n";
    
    // 测试生成少量序列号
    echo "🧪 测试生成 100 个序列号...\n";
    $testCount = 0.01; // 0.01万 = 100个
    
    $startTime = microtime(true);
    $result = $generator->generateSerials($testCount);
    $endTime = microtime(true);
    
    if ($result['success']) {
        echo "✅ 测试成功!\n";
        echo "- 批次ID: " . $result['batch_id'] . "\n";
        echo "- 请求生成: " . $result['total_count'] . " 个\n";
        echo "- 实际生成: " . $result['generated_count'] . " 个\n";
        echo "- 成功率: " . round($result['generated_count'] / $result['total_count'] * 100, 2) . "%\n";
        echo "- 耗时: " . round($endTime - $startTime, 2) . " 秒\n\n";
        
        // 验证序列号格式
        echo "🔍 验证序列号格式...\n";
        $pdo = (new Database())->getConnection();
        $stmt = $pdo->prepare("SELECT serial_number FROM serial_numbers WHERE batch_id = ? LIMIT 5");
        $stmt->execute([$result['batch_id']]);
        $samples = $stmt->fetchAll();
        
        echo "样本序列号:\n";
        foreach ($samples as $sample) {
            $serial = $sample['serial_number'];
            echo "- $serial";
            
            // 验证格式
            if (strlen($serial) == 16 && is_numeric($serial) && substr($serial, 0, 4) != '0000') {
                echo " ✅ 格式正确\n";
            } else {
                echo " ❌ 格式错误\n";
            }
        }
        
        // 验证唯一性
        echo "\n🔍 验证序列号唯一性...\n";
        $stmt = $pdo->prepare("SELECT COUNT(*) as total, COUNT(DISTINCT serial_number) as unique_count FROM serial_numbers WHERE batch_id = ?");
        $stmt->execute([$result['batch_id']]);
        $uniqueCheck = $stmt->fetch();
        
        if ($uniqueCheck['total'] == $uniqueCheck['unique_count']) {
            echo "✅ 所有序列号唯一\n";
        } else {
            echo "❌ 发现重复序列号\n";
        }
        
    } else {
        echo "❌ 测试失败: " . $result['message'] . "\n";
    }
    
    // 性能测试
    echo "\n⏱️ 性能测试信息:\n";
    echo "- 平均每秒生成: " . round($result['generated_count'] / ($endTime - $startTime)) . " 个\n";
    echo "- 每万个预估耗时: " . round(10000 / ($result['generated_count'] / ($endTime - $startTime))) . " 秒\n";
    
} catch (Exception $e) {
    echo "❌ 测试失败: " . $e->getMessage() . "\n";
}

echo "\n🎉 测试完成!\n";
?> 