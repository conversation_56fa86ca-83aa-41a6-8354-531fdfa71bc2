<?php
require_once 'config.php';

class SerialGenerator {
    private $db;
    private $batchSize = 10000; // 每批次插入10000条
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * 生成指定数量的序列号
     * @param int $count 生成数量（以万为单位）
     * @return array 结果信息
     */
    public function generateSerials($count) {
        $totalCount = $count * 10000; // 转换为实际数量
        $batchId = $this->generateBatchId();
        
        // 插入任务记录
        $this->insertTask($batchId, $totalCount);
        
        try {
            $generatedCount = 0;
            $attempts = 0;
            $maxAttempts = $totalCount * 2; // 最大尝试次数
            
            while ($generatedCount < $totalCount && $attempts < $maxAttempts) {
                $batch = [];
                $batchAttempts = 0;
                
                // 生成一批序列号
                while (count($batch) < $this->batchSize && $batchAttempts < $this->batchSize * 2) {
                    $serial = $this->generateUniqueSerial();
                    
                    // 检查是否已存在（内存中检查）
                    if (!in_array($serial, $batch)) {
                        $batch[] = $serial;
                    }
                    
                    $batchAttempts++;
                }
                
                // 批量插入数据库
                if (!empty($batch)) {
                    $inserted = $this->insertBatch($batch, $batchId);
                    $generatedCount += $inserted;
                    
                    // 更新任务进度
                    $this->updateTaskProgress($batchId, $generatedCount);
                }
                
                $attempts += $batchAttempts;
                
                // 避免内存溢出
                if ($generatedCount % 50000 == 0) {
                    gc_collect_cycles(); // 垃圾回收
                }
            }
            
            // 完成任务
            $this->completeTask($batchId, $generatedCount);
            
            return [
                'success' => true,
                'message' => "成功生成 {$generatedCount} 个序列号",
                'batch_id' => $batchId,
                'generated_count' => $generatedCount,
                'total_count' => $totalCount
            ];
            
        } catch (Exception $e) {
            $this->failTask($batchId, $e->getMessage());
            return [
                'success' => false,
                'message' => "生成失败: " . $e->getMessage(),
                'batch_id' => $batchId
            ];
        }
    }
    
    /**
     * 生成唯一的16位序列号
     * @return string 16位序列号
     */
    private function generateUniqueSerial() {
        // 前四位不能是0，所以从1000-9999
        $prefix = str_pad(mt_rand(1000, 9999), 4, '0', STR_PAD_LEFT);
        
        // 后12位随机数字
        $suffix = '';
        for ($i = 0; $i < 12; $i++) {
            $suffix .= mt_rand(0, 9);
        }
        
        return $prefix . $suffix;
    }
    
    /**
     * 批量插入序列号到数据库
     * @param array $batch 序列号数组
     * @param string $batchId 批次ID
     * @return int 实际插入数量
     */
    private function insertBatch($batch, $batchId) {
        $pdo = $this->db->getConnection();
        $insertedCount = 0;
        
        try {
            $pdo->beginTransaction();
            
            // 根据数据库类型选择不同的SQL语句
            $dbType = $this->db->getDbType();
            
            if ($dbType === 'sqlite') {
                // SQLite 使用 INSERT OR IGNORE
                $placeholders = str_repeat('(?,?),', count($batch));
                $placeholders = rtrim($placeholders, ',');
                $sql = "INSERT OR IGNORE INTO serial_numbers (serial_number, batch_id) VALUES {$placeholders}";
            } else {
                // MySQL 使用 INSERT IGNORE
                $placeholders = str_repeat('(?,?),', count($batch));
                $placeholders = rtrim($placeholders, ',');
                $sql = "INSERT IGNORE INTO serial_numbers (serial_number, batch_id) VALUES {$placeholders}";
            }
            
            $stmt = $pdo->prepare($sql);
            
            // 绑定参数
            $params = [];
            foreach ($batch as $serial) {
                $params[] = $serial;
                $params[] = $batchId;
            }
            
            $stmt->execute($params);
            $insertedCount = $stmt->rowCount();
            
            $pdo->commit();
            
        } catch (Exception $e) {
            $pdo->rollback();
            throw $e;
        }
        
        return $insertedCount;
    }
    
    /**
     * 生成批次ID
     * @return string 批次ID
     */
    private function generateBatchId() {
        return 'BATCH_' . date('YmdHis') . '_' . uniqid();
    }
    
    /**
     * 插入任务记录
     * @param string $batchId 批次ID
     * @param int $totalCount 总数量
     */
    private function insertTask($batchId, $totalCount) {
        $pdo = $this->db->getConnection();
        $sql = "INSERT INTO generation_tasks (batch_id, total_count, status) VALUES (?, ?, 0)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$batchId, $totalCount]);
    }
    
    /**
     * 更新任务进度
     * @param string $batchId 批次ID
     * @param int $generatedCount 已生成数量
     */
    private function updateTaskProgress($batchId, $generatedCount) {
        $pdo = $this->db->getConnection();
        $sql = "UPDATE generation_tasks SET generated_count = ? WHERE batch_id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$generatedCount, $batchId]);
    }
    
    /**
     * 完成任务
     * @param string $batchId 批次ID
     * @param int $generatedCount 已生成数量
     */
    private function completeTask($batchId, $generatedCount) {
        $pdo = $this->db->getConnection();
        
        // 根据数据库类型选择不同的时间函数
        $dbType = $this->db->getDbType();
        $timeFunc = ($dbType === 'sqlite') ? 'CURRENT_TIMESTAMP' : 'NOW()';
        
        $sql = "UPDATE generation_tasks SET generated_count = ?, status = 1, completed_at = {$timeFunc} WHERE batch_id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$generatedCount, $batchId]);
    }
    
    /**
     * 失败任务
     * @param string $batchId 批次ID
     * @param string $errorMessage 错误信息
     */
    private function failTask($batchId, $errorMessage) {
        $pdo = $this->db->getConnection();
        $sql = "UPDATE generation_tasks SET status = 2, error_message = ? WHERE batch_id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$errorMessage, $batchId]);
    }
    
    /**
     * 获取任务状态
     * @param string $batchId 批次ID
     * @return array 任务信息
     */
    public function getTaskStatus($batchId) {
        $pdo = $this->db->getConnection();
        $sql = "SELECT * FROM generation_tasks WHERE batch_id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$batchId]);
        return $stmt->fetch();
    }
    
    /**
     * 获取序列号统计信息
     * @return array 统计信息
     */
    public function getStatistics() {
        $pdo = $this->db->getConnection();
        $sql = "SELECT COUNT(*) as total_serials FROM serial_numbers";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        return $stmt->fetch();
    }
}
?> 