# 序列号生成器

一个基于PHP 8.0的高性能序列号生成系统，支持生成指定数量的16位无重复数字序列号。

## ✨ 功能特点

- 🎯 生成16位纯数字序列号（前4位不为0000）
- 🔄 保证序列号唯一性
- 📊 支持大批量生成（十万以上）
- 🚀 批量处理（每批10,000条）
- 💻 Web界面和命令行双模式
- 🗄️ 双数据库支持（MySQL + SQLite）
- 🔗 RESTful API接口支持
- 📝 详细的生成日志记录
- 🎨 美观的现代化界面
- 🔄 一键数据库切换

## 📋 系统要求

- PHP 8.0+
- MySQL 5.7+ 或 SQLite 3.0+
- Apache/Nginx Web服务器
- 至少512MB内存

## 🛠️ 安装配置

### 1. 数据库配置

系统支持两种数据库：MySQL 和 SQLite

#### 🔗 MySQL配置

首先导入数据库结构：

```sql
mysql -u root -p < database.sql
```

然后修改 `config.php` 中的数据库连接信息：

```php
define('DB_TYPE', 'mysql');        // 数据库类型
define('DB_HOST', 'localhost');    // 数据库主机
define('DB_NAME', 'serial_generator'); // 数据库名
define('DB_USER', 'root');         // 数据库用户名
define('DB_PASS', '');             // 数据库密码
```

#### 📁 SQLite配置

使用SQLite更加简单，无需安装数据库服务器：

```php
define('DB_TYPE', 'sqlite');       // 数据库类型
define('SQLITE_DB_PATH', __DIR__ . '/serial_generator.db'); // 数据库文件路径
```

**SQLite优势：**
- 无需安装数据库服务器
- 零配置，开箱即用
- 数据存储在单个文件中，便于备份和迁移
- 适合中小型项目和开发测试

**注意：** 使用SQLite时，表结构会在首次运行时自动创建，无需手动导入SQL文件。

### 2. 文件权限设置

确保以下文件具有写入权限：
- `generation.log` - 生成日志
- `error.log` - 错误日志
- `verify.log` - 验证页面日志
- `export.log` - 导出操作日志
- `csv/` - CSV导出文件夹

```bash
chmod 666 generation.log error.log verify.log export.log
chmod 755 csv/
```

### 3. Web服务器配置

将项目文件放置在Web服务器根目录下，确保可以通过浏览器访问 `index.html`。

## 🚀 使用方法

### Web界面方式

1. 在浏览器中打开 `index.html`
2. 输入需要生成的序列号数量（以万为单位）
3. 点击"开始生成序列号"按钮
4. 等待生成完成，查看结果统计

### 命令行方式

```bash
# 生成10万个序列号
php cli_generator.php 10

# 生成100万个序列号
php cli_generator.php 100

# 查看帮助信息
php cli_generator.php --help
```

### 测试方式

```bash
# 测试MySQL配置
php test.php

# 测试SQLite配置
php test_sqlite.php
```

### 数据库切换演示

```bash
# 切换到SQLite数据库
php demo.php sqlite

# 切换到MySQL数据库
php demo.php mysql

# 测试当前数据库连接
php demo.php test

# 比较两种数据库
php demo.php compare

# 恢复配置备份
php demo.php restore
```

### API接口使用

```bash
# 启动API服务器
php -S localhost:8080

# 检查序列号是否存在
curl "http://localhost:8080/check_serial.php?serial=1234567890123456"

# 获取数据库统计信息
curl "http://localhost:8080/check_serial.php?action=stats"
```

### 批量导出使用

```bash
# 启动Web服务器
php -S localhost:8080

# 访问导出页面
# http://localhost:8080/export.php

# 或者通过主页导航访问
# http://localhost:8080/index.html → 导出功能
```

**JavaScript示例**：
```javascript
// 检查序列号
fetch('/check_serial.php?serial=1234567890123456')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log('序列号存在:', data.data);
    } else {
      console.log('序列号不存在:', data.message);
    }
  });
```

**详细API文档**: 请查看 [API_Documentation.md](API_Documentation.md)

## 🔐 序列号验证页面

系统提供了美观的Web界面用于验证序列号：

### 访问方式
1. 启动PHP开发服务器：`php -S localhost:8080`
2. 访问：`http://localhost:8080/verify.php`

### 功能特点
- **用户友好界面**：现代化的响应式设计
- **实时验证**：输入时自动格式化，只允许数字
- **完整验证**：检查格式、长度、前缀等所有规则
- **详细结果**：显示序列号信息、批次ID、创建时间等
- **错误处理**：清晰的错误提示和分类
- **自动日志**：记录所有验证操作到 `verify.log`

### 验证规则
- 序列号必须为16位纯数字
- 前4位不能为0000
- 必须存在于数据库中

### 测试用例
可以使用以下序列号进行测试：
- **存在的序列号**：从数据库中获取（如：1000005373505921）
- **格式错误**：12345（长度不足）
- **包含字母**：abcd567890123456
- **前缀错误**：0000567890123456

## 📤 序列号批量导出

系统提供了强大的批量导出功能，可以按ID范围导出序列号为CSV文件：

### 访问方式
1. 启动PHP开发服务器：`php -S localhost:8080`
2. 访问：`http://localhost:8080/export.php`

### 功能特点
- **按范围导出**：指定起始和截止ID（包含边界值）
- **自动统计**：显示数据库总记录数、ID范围等信息
- **安全限制**：单次最多导出100,000条记录，防止内存溢出
- **智能命名**：文件名采用日期_数量格式（如：20240101_1000.csv）
- **UTF-8编码**：支持中文，Excel可正确打开
- **详细结果**：显示导出数量、文件大小、保存路径等
- **自动日志**：记录所有导出操作到 `export.log`

### CSV文件格式
导出的CSV文件包含以下字段：
- **ID**：数据库记录ID
- **序列号**：16位数字序列号
- **批次ID**：生成批次标识
- **创建时间**：记录创建时间
- **更新时间**：记录更新时间

### 使用示例
1. 导出前1000条记录：起始ID=1，截止ID=1000
2. 导出指定范围：起始ID=5000，截止ID=10000
3. 系统会自动验证ID范围，确保不超过数据库最大值

### 文件存储
- 所有CSV文件保存在 `./csv/` 文件夹中
- 文件命名：`YYYYMMDD_数量.csv`
- 如果同名文件存在，会自动添加时间戳避免冲突

## 📁 文件结构

```
序列号生成器/
├── config.php          # 数据库配置文件
├── database.sql        # MySQL数据库结构文件
├── database_sqlite.sql # SQLite数据库结构文件（参考）
├── generator.php       # 序列号生成器核心类
├── index.html          # Web界面
├── process.php         # Web请求处理
├── cli_generator.php   # 命令行可执行文件
├── check_serial.php    # 序列号检测API接口
├── verify.php          # 序列号验证页面
├── export.php          # 序列号批量导出页面
├── test.php            # MySQL测试脚本
├── test_sqlite.php     # SQLite测试脚本
├── demo.php            # 数据库切换演示脚本
├── README.md           # 项目说明文档
├── API_Documentation.md # API接口文档
├── generation.log      # 生成日志（自动创建）
├── error.log           # 错误日志（自动创建）
├── api.log             # API查询日志（自动创建）
├── verify.log          # 验证页面日志（自动创建）
├── export.log          # 导出操作日志（自动创建）
├── csv/                # CSV导出文件夹（自动创建）
│   └── *.csv           # 导出的CSV文件
└── serial_generator.db # SQLite数据库文件（使用SQLite时自动创建）
```

## 🗄️ 数据库表结构

### serial_numbers（序列号表）

| 字段名 | MySQL类型 | SQLite类型 | 说明 |
|--------|-----------|------------|------|
| id | bigint | INTEGER | 主键ID |
| serial_number | varchar(16) | TEXT | 16位序列号 |
| batch_id | varchar(32) | TEXT | 批次ID |
| created_at | timestamp | DATETIME | 创建时间 |
| updated_at | timestamp | DATETIME | 更新时间 |

### generation_tasks（生成任务表）

| 字段名 | MySQL类型 | SQLite类型 | 说明 |
|--------|-----------|------------|------|
| id | int | INTEGER | 主键ID |
| batch_id | varchar(32) | TEXT | 批次ID |
| total_count | int | INTEGER | 总生成数量 |
| generated_count | int | INTEGER | 已生成数量 |
| status | tinyint | INTEGER | 状态（0-进行中，1-已完成，2-失败） |
| error_message | text | TEXT | 错误信息 |
| started_at | timestamp | DATETIME | 开始时间 |
| completed_at | timestamp | DATETIME | 完成时间 |

## ⚙️ 性能优化

### 批量处理
- 每批处理10,000条记录
- 使用事务确保数据一致性
- 批量插入减少数据库交互次数

### 内存管理
- 设置内存限制为512MB
- 定期执行垃圾回收
- 避免内存溢出

### 唯一性保证
- 数据库级别的唯一约束
- 使用 `INSERT IGNORE` 处理重复
- 内存中预检查减少数据库压力

## 🔧 配置参数

### 生成器配置
```php
private $batchSize = 10000;  // 每批次处理数量
```

### PHP配置
```php
set_time_limit(0);           // 无时间限制
ini_set('memory_limit', '512M'); // 内存限制
```

## 📊 监控和日志

### 生成日志 (generation.log)
记录成功的生成操作：
```
2024-01-01 12:00:00 - 成功生成 100000 个序列号，耗时 25.3 秒
```

### 错误日志 (error.log)
记录失败的操作：
```
2024-01-01 12:00:00 - 生成失败: 数据库连接失败
```

### 验证日志 (verify.log)
记录验证页面的操作：
```
2024-01-01 12:00:00 - 页面验证: serial=1234567890123456, result=success, type=success
2024-01-01 12:01:00 - 页面验证: serial=12345, result=failed, type=format_error
```

### 导出日志 (export.log)
记录批量导出的操作：
```
2024-01-01 12:00:00 - 导出成功: 范围=1-1000, 数量=1000, 文件=20240101_1000.csv
2024-01-01 12:05:00 - 导出失败: 指定ID范围内没有找到任何序列号
```

## 🎯 序列号规则

1. **长度**：固定16位数字
2. **前缀**：前4位范围1000-9999（不能是0000）
3. **后缀**：后12位随机数字0-9
4. **唯一性**：全局唯一，不允许重复
5. **格式**：纯数字，无分隔符

示例序列号：
- `1234567890123456`
- `9876543210987654`
- `5555666677778888`

## 🚨 注意事项

1. **大批量生成**：生成大量序列号时请耐心等待
2. **内存使用**：监控系统内存使用情况
3. **数据库连接**：确保数据库连接稳定
4. **权限设置**：确保日志文件有写入权限
5. **备份数据**：定期备份生成的序列号数据

## 🔄 数据库选择建议

### MySQL vs SQLite

| 特性 | MySQL | SQLite |
|------|-------|--------|
| 安装配置 | 需要安装数据库服务器 | 无需安装，开箱即用 |
| 性能 | 高并发性能优异 | 适合中小型应用 |
| 数据存储 | 服务器端存储 | 单文件存储 |
| 备份迁移 | 需要数据库工具 | 直接复制文件即可 |
| 适用场景 | 生产环境，大量数据 | 开发测试，小型项目 |

### 建议

- **开发测试**：推荐使用SQLite，零配置，便于测试
- **生产环境**：推荐使用MySQL，性能更好，支持并发
- **小型项目**：SQLite完全够用，部署简单
- **大型项目**：MySQL更稳定，扩展性更好

### 切换数据库

只需修改 `config.php` 中的 `DB_TYPE` 配置：

```php
// 使用MySQL
define('DB_TYPE', 'mysql');

// 使用SQLite
define('DB_TYPE', 'sqlite');
```

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   - **MySQL**: 检查数据库配置信息，确认数据库服务是否运行，验证用户权限
   - **SQLite**: 检查数据库文件路径是否正确，确认目录写入权限

2. **内存不足**
   - 增加PHP内存限制
   - 减少每批处理数量
   - 定期清理临时数据

3. **生成速度慢**
   - 检查数据库性能
   - 优化数据库索引
   - 调整批处理大小
   - **SQLite**: 考虑切换到MySQL以获得更好性能

4. **序列号重复**
   - 检查唯一约束是否生效
   - 验证随机数生成器
   - 清理重复数据

5. **SQLite特有问题**
   - **文件权限**: 确保SQLite数据库文件所在目录有写入权限
   - **并发问题**: SQLite不支持高并发写入，避免同时运行多个生成任务
   - **数据库锁定**: 如果出现数据库锁定，检查是否有其他进程在使用数据库

### 测试和调试

```bash
# 测试MySQL配置
php test.php

# 测试SQLite配置
php test_sqlite.php

# 检查数据库连接
php -r "require 'config.php'; $db = new Database(); echo 'Database connection successful';"
```

## 📄 许可证

本项目遵循 MIT 许可证。

## 👨‍💻 开发者

由资深PHP开发工程师开发，专注于高性能数据处理系统。

---

如有问题或建议，请联系开发团队。 