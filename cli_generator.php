#!/usr/bin/env php
<?php
/**
 * 命令行序列号生成器
 * 使用方法: php cli_generator.php [数量(万为单位)]
 * 例如: php cli_generator.php 10  // 生成10万个序列号
 */

// 设置PHP执行时间限制
set_time_limit(0);
ini_set('memory_limit', '512M');

require_once 'generator.php';

// 颜色输出函数
function colorOutput($text, $color = 'white') {
    $colors = [
        'red' => '31',
        'green' => '32',
        'yellow' => '33',
        'blue' => '34',
        'magenta' => '35',
        'cyan' => '36',
        'white' => '37',
        'gray' => '90'
    ];
    
    $colorCode = isset($colors[$color]) ? $colors[$color] : '37';
    return "\033[{$colorCode}m{$text}\033[0m";
}

// 显示使用帮助
function showHelp() {
    echo colorOutput("序列号生成器 - 命令行版本\n", 'cyan');
    echo colorOutput("==========================\n", 'cyan');
    echo "使用方法: php cli_generator.php [数量(万为单位)]\n";
    echo "参数说明:\n";
    echo "  数量: 1-1000 (以万为单位)\n";
    echo "示例:\n";
    echo colorOutput("  php cli_generator.php 10\n", 'yellow');
    echo "  // 生成10万个序列号\n\n";
    echo colorOutput("  php cli_generator.php 100\n", 'yellow');
    echo "  // 生成100万个序列号\n\n";
    echo "注意事项:\n";
    echo "- 序列号为16位纯数字，前4位不为0000\n";
    echo "- 每批次处理1万条记录\n";
    echo "- 所有序列号保证唯一性\n";
    echo "- 生成大量序列号时请耐心等待\n";
}

// 显示进度条
function showProgress($current, $total, $message = '') {
    $percent = round(($current / $total) * 100);
    $bar = str_repeat('█', floor($percent / 5));
    $space = str_repeat('░', 20 - floor($percent / 5));
    
    echo "\r" . colorOutput("进度: ", 'cyan') . "[$bar$space] " . 
         colorOutput("$percent%", 'green') . 
         colorOutput(" ($current/$total)", 'gray') . 
         ($message ? " - $message" : '');
}

// 主程序
function main() {
    global $argc, $argv;
    
    echo colorOutput("🔢 序列号生成器 - 命令行版本\n", 'cyan');
    echo colorOutput("=====================================\n", 'cyan');
    
    // 检查参数
    if ($argc < 2) {
        echo colorOutput("错误: 缺少参数\n", 'red');
        showHelp();
        exit(1);
    }
    
    if ($argv[1] == '-h' || $argv[1] == '--help') {
        showHelp();
        exit(0);
    }
    
    $count = intval($argv[1]);
    
    if ($count <= 0 || $count > 1000) {
        echo colorOutput("错误: 生成数量必须在1-1000万之间\n", 'red');
        exit(1);
    }
    
    try {
        echo colorOutput("准备生成 {$count} 万个序列号...\n", 'yellow');
        echo colorOutput("配置信息:\n", 'cyan');
        echo "- 数据库: " . DB_HOST . "/" . DB_NAME . "\n";
        echo "- 批次大小: 10,000 条/批\n";
        echo "- 总批次数: " . ($count * 10000 / 10000) . " 批\n";
        echo colorOutput("开始生成...\n\n", 'green');
        
        // 记录开始时间
        $startTime = microtime(true);
        
        // 创建生成器实例
        $generator = new SerialGenerator();
        
        // 生成序列号
        $result = $generator->generateSerials($count);
        
        // 记录结束时间
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);
        
        echo "\n\n";
        
        if ($result['success']) {
            echo colorOutput("✅ 生成成功!\n", 'green');
            echo colorOutput("生成结果:\n", 'cyan');
            echo "- 批次ID: " . $result['batch_id'] . "\n";
            echo "- 请求生成: " . number_format($result['total_count']) . " 个\n";
            echo "- 实际生成: " . number_format($result['generated_count']) . " 个\n";
            echo "- 成功率: " . round($result['generated_count'] / $result['total_count'] * 100, 2) . "%\n";
            echo "- 执行时间: " . $executionTime . " 秒\n";
            
            // 获取统计信息
            $stats = $generator->getStatistics();
            echo "- 数据库总数: " . number_format($stats['total_serials']) . " 个\n";
            
            // 记录日志
            $logMessage = date('Y-m-d H:i:s') . " - CLI生成成功: {$result['generated_count']} 个序列号，耗时 {$executionTime} 秒\n";
            file_put_contents('generation.log', $logMessage, FILE_APPEND | LOCK_EX);
            
        } else {
            echo colorOutput("❌ 生成失败!\n", 'red');
            echo colorOutput("错误信息: " . $result['message'] . "\n", 'red');
            
            // 记录错误日志
            $errorMessage = date('Y-m-d H:i:s') . " - CLI生成失败: " . $result['message'] . "\n";
            file_put_contents('error.log', $errorMessage, FILE_APPEND | LOCK_EX);
        }
        
    } catch (Exception $e) {
        echo colorOutput("\n❌ 发生异常: " . $e->getMessage() . "\n", 'red');
        
        // 记录错误日志
        $errorMessage = date('Y-m-d H:i:s') . " - CLI异常: " . $e->getMessage() . "\n";
        file_put_contents('error.log', $errorMessage, FILE_APPEND | LOCK_EX);
        
        exit(1);
    }
}

// 运行主程序
main();
?> 