<?php
/**
 * SQLite版本序列号生成器测试脚本
 * 使用前请先在config.php中将DB_TYPE设置为'sqlite'
 */

// 临时设置为SQLite模式进行测试
define('DB_TYPE_TEMP', 'sqlite');

// 创建临时配置
$configContent = file_get_contents('config.php');
$testConfigContent = str_replace("define('DB_TYPE', 'mysql');", "define('DB_TYPE', 'sqlite');", $configContent);
file_put_contents('config_test.php', $testConfigContent);

// 包含临时配置
require_once 'config_test.php';

// 修改generator.php的引用
$generatorContent = file_get_contents('generator.php');
$testGeneratorContent = str_replace("require_once 'config.php';", "require_once 'config_test.php';", $generatorContent);
file_put_contents('generator_test.php', $testGeneratorContent);

require_once 'generator_test.php';

echo "🔢 序列号生成器 - SQLite测试脚本\n";
echo "================================\n\n";

try {
    // 删除可能存在的测试数据库
    $testDbPath = __DIR__ . '/test_serial_generator.db';
    if (file_exists($testDbPath)) {
        unlink($testDbPath);
        echo "🗑️ 删除旧的测试数据库文件\n";
    }
    
    // 临时修改数据库路径
    $originalPath = SQLITE_DB_PATH;
    $reflectionClass = new ReflectionClass('Database');
    $reflectionProperty = $reflectionClass->getProperty('pdo');
    $reflectionProperty->setAccessible(true);
    
    echo "📡 测试SQLite数据库连接...\n";
    
    // 创建SQLite数据库连接
    $dsn = "sqlite:" . $testDbPath;
    $pdo = new PDO($dsn, null, null, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    // 启用外键约束
    $pdo->exec('PRAGMA foreign_keys = ON');
    
    // 创建测试表
    $pdo->exec("
        CREATE TABLE serial_numbers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            serial_number TEXT NOT NULL UNIQUE,
            batch_id TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ");
    
    $pdo->exec("
        CREATE TABLE generation_tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            batch_id TEXT NOT NULL UNIQUE,
            total_count INTEGER NOT NULL,
            generated_count INTEGER DEFAULT 0,
            status INTEGER DEFAULT 0,
            error_message TEXT,
            started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            completed_at DATETIME
        )
    ");
    
    // 创建索引
    $pdo->exec("CREATE INDEX idx_batch_id ON serial_numbers(batch_id)");
    $pdo->exec("CREATE INDEX idx_created_at ON serial_numbers(created_at)");
    $pdo->exec("CREATE INDEX idx_serial_number_prefix ON serial_numbers(substr(serial_number, 1, 4))");
    $pdo->exec("CREATE INDEX idx_status ON generation_tasks(status)");
    
    echo "✅ SQLite数据库连接成功\n";
    echo "📁 数据库文件路径: $testDbPath\n\n";
    
    // 测试插入数据
    echo "🧪 测试SQLite INSERT OR IGNORE语法...\n";
    
    $testSerial = '1234567890123456';
    $testBatchId = 'TEST_BATCH_' . time();
    
    // 插入测试数据
    $stmt = $pdo->prepare("INSERT OR IGNORE INTO serial_numbers (serial_number, batch_id) VALUES (?, ?)");
    $stmt->execute([$testSerial, $testBatchId]);
    
    // 再次插入相同数据（应该被忽略）
    $stmt->execute([$testSerial, $testBatchId]);
    
    // 检查数据
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM serial_numbers WHERE serial_number = ?");
    $stmt->execute([$testSerial]);
    $result = $stmt->fetch();
    
    if ($result['count'] == 1) {
        echo "✅ INSERT OR IGNORE 语法测试通过\n";
    } else {
        echo "❌ INSERT OR IGNORE 语法测试失败\n";
    }
    
    // 测试时间函数
    echo "🕐 测试SQLite时间函数...\n";
    $stmt = $pdo->prepare("INSERT INTO generation_tasks (batch_id, total_count, completed_at) VALUES (?, ?, CURRENT_TIMESTAMP)");
    $stmt->execute([$testBatchId, 100]);
    
    $stmt = $pdo->prepare("SELECT completed_at FROM generation_tasks WHERE batch_id = ?");
    $stmt->execute([$testBatchId]);
    $result = $stmt->fetch();
    
    if ($result['completed_at']) {
        echo "✅ CURRENT_TIMESTAMP 函数测试通过: " . $result['completed_at'] . "\n";
    } else {
        echo "❌ CURRENT_TIMESTAMP 函数测试失败\n";
    }
    
    // 测试基本查询
    echo "📊 测试基本查询功能...\n";
    $stmt = $pdo->prepare("SELECT COUNT(*) as total_serials FROM serial_numbers");
    $stmt->execute();
    $stats = $stmt->fetch();
    
    echo "✅ 查询成功，当前序列号数量: " . $stats['total_serials'] . "\n";
    
    // 测试前缀查询
    echo "🔍 测试前缀查询...\n";
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM serial_numbers WHERE substr(serial_number, 1, 4) = '1234'");
    $stmt->execute();
    $prefixResult = $stmt->fetch();
    
    echo "✅ 前缀查询成功，前缀为1234的序列号数量: " . $prefixResult['count'] . "\n";
    
    echo "\n🎉 SQLite测试完成！\n";
    echo "💡 如要使用SQLite，请在config.php中将DB_TYPE设置为'sqlite'\n";
    
} catch (Exception $e) {
    echo "❌ SQLite测试失败: " . $e->getMessage() . "\n";
} finally {
    // 清理临时文件
    if (file_exists('config_test.php')) {
        unlink('config_test.php');
    }
    if (file_exists('generator_test.php')) {
        unlink('generator_test.php');
    }
    if (file_exists($testDbPath)) {
        echo "🗑️ 清理测试数据库文件\n";
        unlink($testDbPath);
    }
}
?> 