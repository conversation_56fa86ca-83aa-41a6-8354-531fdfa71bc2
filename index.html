<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>序列号生成器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 90%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
            font-size: 1.1em;
        }

        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }

        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .result.loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .stats {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .stats h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .stats p {
            color: #666;
            margin: 5px 0;
        }

        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .note h4 {
            margin-bottom: 8px;
        }

        .note ul {
            margin-left: 20px;
        }

        .note li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔢 序列号生成器</h1>
            <p>生成指定数量的16位无重复序列号</p>
        </div>

        <div style="text-align: center; margin-bottom: 20px;">
            <a href="verify.php" style="display: inline-block; margin: 0 10px; padding: 10px 20px; background: #74b9ff; color: white; text-decoration: none; border-radius: 5px; font-size: 0.9em;">🔍 验证序列号</a>
            <a href="export.php" style="display: inline-block; margin: 0 10px; padding: 10px 20px; background: #00b894; color: white; text-decoration: none; border-radius: 5px; font-size: 0.9em;">📤 导出CSV</a>
            <a href="check_serial.php?action=stats" style="display: inline-block; margin: 0 10px; padding: 10px 20px; background: #e17055; color: white; text-decoration: none; border-radius: 5px; font-size: 0.9em;">📊 查看统计</a>
        </div>

        <div class="note">
            <h4>📋 使用说明：</h4>
            <ul>
                <li>序列号为16位纯数字，前4位不为0000</li>
                <li>生成数量以万为单位（如：输入10表示生成10万个）</li>
                <li>每批次处理1万条记录，大量生成请耐心等待</li>
                <li>所有序列号保证唯一性</li>
            </ul>
        </div>

        <form id="generateForm">
            <div class="form-group">
                <label for="count">生成数量（万为单位）：</label>
                <input type="number" id="count" name="count" min="1" max="1000" value="10" required>
            </div>
            
            <button type="submit" class="btn" id="generateBtn">
                开始生成序列号
            </button>
        </form>

        <div id="result" class="result">
            <div id="resultContent"></div>
        </div>

        <div id="stats" class="stats" style="display: none;">
            <h3>📊 生成统计</h3>
            <div id="statsContent"></div>
        </div>
    </div>

    <script>
        document.getElementById('generateForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const count = document.getElementById('count').value;
            const btn = document.getElementById('generateBtn');
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            const stats = document.getElementById('stats');
            
            if (count < 1 || count > 1000) {
                showResult('error', '生成数量必须在1-1000万之间！');
                return;
            }
            
            // 显示加载状态
            btn.disabled = true;
            btn.innerHTML = '<span class="loading-spinner"></span>正在生成中...';
            showResult('loading', `正在生成${count}万个序列号，请耐心等待...`);
            
            // 发送AJAX请求
            fetch('process.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `count=${count}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult('success', `✅ ${data.message}`);
                    showStats(data);
                } else {
                    showResult('error', `❌ ${data.message}`);
                }
            })
            .catch(error => {
                showResult('error', `❌ 请求失败: ${error.message}`);
            })
            .finally(() => {
                btn.disabled = false;
                btn.innerHTML = '开始生成序列号';
            });
        });
        
        function showResult(type, message) {
            const result = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            
            result.className = `result ${type}`;
            resultContent.innerHTML = message;
            result.style.display = 'block';
        }
        
        function showStats(data) {
            const stats = document.getElementById('stats');
            const statsContent = document.getElementById('statsContent');
            
            if (data.success) {
                statsContent.innerHTML = `
                    <p><strong>批次ID:</strong> ${data.batch_id}</p>
                    <p><strong>请求生成:</strong> ${data.total_count.toLocaleString()} 个</p>
                    <p><strong>实际生成:</strong> ${data.generated_count.toLocaleString()} 个</p>
                    <p><strong>成功率:</strong> ${(data.generated_count / data.total_count * 100).toFixed(2)}%</p>
                    <p><strong>生成时间:</strong> ${new Date().toLocaleString()}</p>
                `;
                stats.style.display = 'block';
            }
        }
    </script>
</body>
</html> 