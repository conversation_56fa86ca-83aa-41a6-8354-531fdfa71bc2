# 序列号检测API接口文档

## 🔗 API概述

序列号检测API提供RESTful接口，用于检查16位序列号是否存在于数据库中。支持MySQL和SQLite双数据库，具备完整的格式验证和错误处理机制。

## 📋 基础信息

- **接口文件**: `check_serial.php`
- **请求方法**: GET
- **响应格式**: JSON
- **字符编码**: UTF-8
- **支持数据库**: MySQL, SQLite

## 🚀 API接口

### 1. 检查序列号

检查指定序列号是否存在于数据库中。

#### 请求

```http
GET /check_serial.php?serial={序列号}
```

#### 参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| serial | string | 是 | 要查询的16位序列号 |

#### 响应格式

**序列号存在时**：
```json
{
  "success": true,
  "code": 200,
  "message": "序列号存在",
  "data": {
    "serial_number": "1234567890123456",
    "batch_id": "BATCH_20240101120000_abc123",
    "created_at": "2024-01-01 12:00:00",
    "exists": true,
    "format_valid": true,
    "database_type": "sqlite"
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

**序列号不存在时**：
```json
{
  "success": false,
  "code": 200,
  "message": "序列号不存在",
  "data": {
    "serial_number": "9999999999999999",
    "exists": false,
    "format_valid": true,
    "database_type": "sqlite"
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

**格式错误时**：
```json
{
  "success": false,
  "code": 200,
  "message": "序列号长度必须为16位",
  "data": {
    "serial_number": "12345",
    "exists": false,
    "format_valid": false
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

### 2. 获取统计信息

获取数据库中序列号的统计信息。

#### 请求

```http
GET /check_serial.php?action=stats
```

#### 响应格式

```json
{
  "success": true,
  "code": 200,
  "message": "获取统计信息成功",
  "data": {
    "total_serials": "60000",
    "latest_created": "2024-01-01 12:00:00",
    "oldest_created": "2024-01-01 10:00:00",
    "database_type": "sqlite"
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

## 📝 使用示例

### JavaScript (Fetch API)

```javascript
// 检查序列号
async function checkSerial(serialNumber) {
  try {
    const response = await fetch(`/check_serial.php?serial=${serialNumber}`);
    const data = await response.json();
    
    if (data.success) {
      console.log('序列号存在:', data.data);
    } else {
      console.log('序列号不存在或格式错误:', data.message);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
}

// 获取统计信息
async function getStats() {
  try {
    const response = await fetch('/check_serial.php?action=stats');
    const data = await response.json();
    console.log('统计信息:', data.data);
  } catch (error) {
    console.error('请求失败:', error);
  }
}

// 使用示例
checkSerial('1234567890123456');
getStats();
```

### PHP (cURL)

```php
// 检查序列号
function checkSerial($serialNumber) {
    $url = "http://your-domain.com/check_serial.php?serial=" . urlencode($serialNumber);
    
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    
    if ($httpCode === 200 && $response) {
        return json_decode($response, true);
    }
    
    return false;
}

// 使用示例
$result = checkSerial('1234567890123456');
if ($result && $result['success']) {
    echo "序列号存在\n";
} else {
    echo "序列号不存在\n";
}
```

### Python (requests)

```python
import requests

def check_serial(serial_number):
    url = f"http://your-domain.com/check_serial.php?serial={serial_number}"
    
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        return data
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None

# 使用示例
result = check_serial('1234567890123456')
if result and result['success']:
    print('序列号存在')
else:
    print('序列号不存在')
```

## 🔍 序列号格式验证

API会自动验证序列号格式，包含以下检查：

1. **长度检查**: 必须为16位
2. **字符检查**: 必须为纯数字
3. **前缀检查**: 前4位不能为0000
4. **空值检查**: 不能为空

### 格式验证错误消息

| 错误情况 | 错误消息 |
|----------|----------|
| 序列号为空 | "序列号不能为空" |
| 长度不正确 | "序列号长度必须为16位" |
| 包含非数字字符 | "序列号必须为纯数字" |
| 前4位为0000 | "序列号前4位不能为0000" |

## 📊 响应字段说明

### 基础响应字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| success | boolean | 操作是否成功 |
| code | integer | HTTP状态码（固定200） |
| message | string | 响应消息 |
| data | object | 响应数据 |
| timestamp | string | 响应时间戳 |

### 数据字段 (data)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| serial_number | string | 查询的序列号 |
| batch_id | string | 批次ID（仅存在时返回） |
| created_at | string | 创建时间（仅存在时返回） |
| exists | boolean | 序列号是否存在 |
| format_valid | boolean | 格式是否正确 |
| database_type | string | 数据库类型 |

## ⚠️ 错误处理

### HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功（包括查询不到的情况） |
| 405 | 请求方法不允许（非GET请求） |
| 500 | 服务器内部错误 |

### 常见错误场景

1. **数据库连接失败**
```json
{
  "success": false,
  "code": 200,
  "message": "查询过程中发生错误: 数据库连接失败",
  "data": {
    "serial_number": "1234567890123456",
    "exists": false,
    "error": "数据库连接失败"
  },
  "timestamp": "2024-01-01 12:00:00"
}
```

2. **请求方法错误**
```json
{
  "success": false,
  "code": 405,
  "message": "只支持GET请求方法",
  "timestamp": "2024-01-01 12:00:00"
}
```

## 📈 性能说明

- **查询速度**: 单次查询通常在10-100ms内完成
- **并发支持**: 支持多用户同时查询
- **数据库索引**: 序列号字段已建立唯一索引，查询效率高
- **缓存**: 建议在客户端实现适当的缓存机制

## 🔒 安全注意事项

1. **输入验证**: API已实现完整的输入验证
2. **SQL注入防护**: 使用预处理语句防止SQL注入
3. **访问控制**: 建议配置适当的访问限制
4. **日志记录**: 所有查询都会记录到`api.log`文件

## 📊 日志格式

API会自动记录查询日志到`api.log`文件：

```
2024-01-01 12:00:00 - API查询: action=check, serial=1234567890123456, result=success
2024-01-01 12:00:01 - API查询: action=check, serial=9999999999999999, result=failed
2024-01-01 12:00:02 - API查询: action=stats, serial=, result=success
```

## 🔧 配置要求

- PHP 8.0+
- MySQL 5.7+ 或 SQLite 3.0+
- 启用PDO扩展
- 写入权限（用于日志文件）

## 🎯 测试工具

可以使用以下工具测试API：

1. **浏览器**: 直接访问URL
2. **Postman**: 发送GET请求
3. **curl**: 命令行测试
4. **在线工具**: 各种REST API测试工具

### curl测试示例

```bash
# 测试存在的序列号
curl "http://localhost/check_serial.php?serial=1234567890123456"

# 测试不存在的序列号
curl "http://localhost/check_serial.php?serial=9999999999999999"

# 获取统计信息
curl "http://localhost/check_serial.php?action=stats"
```

---

**版本**: 1.0  
**更新时间**: 2024年1月1日  
**维护者**: 序列号生成器开发团队 