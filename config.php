<?php
/**
 * 数据库配置文件
 */

// 数据库类型配置: 'mysql' 或 'sqlite'
define('DB_TYPE', 'sqlite'); // 可以改为 'sqlite'

// MySQL配置
define('DB_HOST', 'localhost');
define('DB_NAME', 'serial_generator');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// SQLite配置
define('SQLITE_DB_PATH', __DIR__ . '/serial_generator.db');

class Database {
    private $pdo;
    
    public function __construct() {
        try {
            if (DB_TYPE === 'sqlite') {
                $this->initSQLite();
            } else {
                $this->initMySQL();
            }
        } catch (PDOException $e) {
            die("数据库连接失败: " . $e->getMessage());
        }
    }
    
    private function initMySQL() {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $this->pdo = new PDO($dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]);
    }
    
    private function initSQLite() {
        $dsn = "sqlite:" . SQLITE_DB_PATH;
        $this->pdo = new PDO($dsn, null, null, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]);
        
        // 启用外键约束
        $this->pdo->exec('PRAGMA foreign_keys = ON');
        
        // 创建表结构（如果不存在）
        $this->createSQLiteTables();
    }
    
    private function createSQLiteTables() {
        // 检查表是否存在
        $stmt = $this->pdo->prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='serial_numbers'");
        $stmt->execute();
        
        if (!$stmt->fetch()) {
            // 创建序列号表
            $sql = "
            CREATE TABLE serial_numbers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                serial_number TEXT NOT NULL UNIQUE,
                batch_id TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )";
            $this->pdo->exec($sql);
            
            // 创建索引
            $this->pdo->exec("CREATE INDEX idx_batch_id ON serial_numbers(batch_id)");
            $this->pdo->exec("CREATE INDEX idx_created_at ON serial_numbers(created_at)");
            $this->pdo->exec("CREATE INDEX idx_serial_number_prefix ON serial_numbers(substr(serial_number, 1, 4))");
            
            // 创建生成任务表
            $sql = "
            CREATE TABLE generation_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                batch_id TEXT NOT NULL UNIQUE,
                total_count INTEGER NOT NULL,
                generated_count INTEGER DEFAULT 0,
                status INTEGER DEFAULT 0,
                error_message TEXT,
                started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                completed_at DATETIME
            )";
            $this->pdo->exec($sql);
            
            // 创建索引
            $this->pdo->exec("CREATE INDEX idx_status ON generation_tasks(status)");
            
            // 创建更新触发器（模拟MySQL的ON UPDATE CURRENT_TIMESTAMP）
            $sql = "
            CREATE TRIGGER update_serial_numbers_timestamp 
            AFTER UPDATE ON serial_numbers
            BEGIN
                UPDATE serial_numbers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END";
            $this->pdo->exec($sql);
        }
    }
    
    public function getConnection() {
        return $this->pdo;
    }
    
    public function getDbType() {
        return DB_TYPE;
    }
}
?> 