<?php
/**
 * 数据库切换演示脚本
 * 演示如何在MySQL和SQLite之间切换
 */

echo "🔢 序列号生成器 - 数据库切换演示\n";
echo "=================================\n\n";

// 演示配置切换
function demoConfigSwitch($dbType) {
    echo "📝 演示切换到 " . strtoupper($dbType) . " 数据库...\n";
    
    $configFile = 'config.php';
    $backupFile = 'config_backup.php';
    
    // 备份当前配置
    if (file_exists($configFile)) {
        copy($configFile, $backupFile);
        echo "✅ 已备份当前配置到 config_backup.php\n";
    }
    
    // 读取配置文件
    $configContent = file_get_contents($configFile);
    
    // 切换数据库类型
    if ($dbType === 'sqlite') {
        $configContent = str_replace("define('DB_TYPE', 'mysql');", "define('DB_TYPE', 'sqlite');", $configContent);
        echo "✅ 已切换到 SQLite 数据库\n";
    } else {
        $configContent = str_replace("define('DB_TYPE', 'sqlite');", "define('DB_TYPE', 'mysql');", $configContent);
        echo "✅ 已切换到 MySQL 数据库\n";
    }
    
    // 写入配置文件
    file_put_contents($configFile, $configContent);
    
    echo "💡 配置已更新，请重新运行应用程序\n\n";
}

// 测试数据库连接
function testDatabaseConnection($dbType) {
    echo "🔍 测试 " . strtoupper($dbType) . " 数据库连接...\n";
    
    try {
        require_once 'config.php';
        require_once 'generator.php';
        $db = new Database();
        
        echo "✅ 数据库连接成功\n";
        echo "📊 数据库类型: " . $db->getDbType() . "\n";
        
        // 获取统计信息
        $generator = new SerialGenerator();
        $stats = $generator->getStatistics();
        echo "📈 当前序列号数量: " . number_format($stats['total_serials']) . " 个\n";
        
        if ($dbType === 'sqlite') {
            echo "📁 SQLite 数据库文件: " . SQLITE_DB_PATH . "\n";
            if (file_exists(SQLITE_DB_PATH)) {
                $fileSize = filesize(SQLITE_DB_PATH);
                echo "💾 文件大小: " . formatBytes($fileSize) . "\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// 格式化字节数
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

// 显示帮助信息
function showHelp() {
    echo "使用方法:\n";
    echo "php demo.php mysql    # 切换到MySQL\n";
    echo "php demo.php sqlite   # 切换到SQLite\n";
    echo "php demo.php test     # 测试当前数据库连接\n";
    echo "php demo.php compare  # 比较两种数据库\n";
    echo "php demo.php restore  # 恢复配置备份\n";
}

// 比较两种数据库
function compareDatabases() {
    echo "📊 MySQL vs SQLite 对比\n";
    echo "========================\n\n";
    
    $comparison = [
        ['特性', 'MySQL', 'SQLite'],
        ['安装', '需要安装数据库服务器', '无需安装'],
        ['配置', '需要配置服务器、用户、密码', '仅需指定文件路径'],
        ['性能', '高并发性能优异', '适合中小型应用'],
        ['存储', '服务器端存储', '单文件存储'],
        ['备份', '需要数据库工具', '直接复制文件'],
        ['部署', '需要数据库环境', '仅需PHP环境'],
        ['维护', '需要定期维护', '几乎无需维护'],
        ['扩展性', '支持集群、分片', '单文件限制'],
        ['适用场景', '生产环境、大型项目', '开发测试、小型项目']
    ];
    
    // 计算最大宽度
    $maxWidths = [0, 0, 0];
    foreach ($comparison as $row) {
        for ($i = 0; $i < 3; $i++) {
            $maxWidths[$i] = max($maxWidths[$i], mb_strlen($row[$i], 'UTF-8'));
        }
    }
    
    // 输出表格
    foreach ($comparison as $index => $row) {
        $line = '| ';
        for ($i = 0; $i < 3; $i++) {
            $line .= str_pad($row[$i], $maxWidths[$i] + 2, ' ', STR_PAD_RIGHT) . ' | ';
        }
        echo $line . "\n";
        
        // 输出分隔线
        if ($index === 0) {
            $line = '|';
            for ($i = 0; $i < 3; $i++) {
                $line .= str_repeat('-', $maxWidths[$i] + 4) . '|';
            }
            echo $line . "\n";
        }
    }
    
    echo "\n";
}

// 恢复配置备份
function restoreConfig() {
    $configFile = 'config.php';
    $backupFile = 'config_backup.php';
    
    if (file_exists($backupFile)) {
        copy($backupFile, $configFile);
        echo "✅ 已恢复配置文件\n";
        unlink($backupFile);
        echo "🗑️ 已删除备份文件\n";
    } else {
        echo "❌ 未找到备份文件\n";
    }
}

// 主程序
if ($argc < 2) {
    showHelp();
    exit(1);
}

$command = $argv[1];

switch ($command) {
    case 'mysql':
        demoConfigSwitch('mysql');
        testDatabaseConnection('mysql');
        break;
        
    case 'sqlite':
        demoConfigSwitch('sqlite');
        testDatabaseConnection('sqlite');
        break;
        
    case 'test':
        echo "🔍 测试当前数据库连接...\n";
        require_once 'config.php';
        require_once 'generator.php';
        testDatabaseConnection(DB_TYPE);
        break;
        
    case 'compare':
        compareDatabases();
        break;
        
    case 'restore':
        restoreConfig();
        break;
        
    default:
        echo "❌ 未知命令: $command\n\n";
        showHelp();
        exit(1);
}

echo "🎉 演示完成！\n";
?> 