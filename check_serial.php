<?php
/**
 * 序列号检测API接口
 * 使用GET方法查询序列号是否存在于数据库中
 * 
 * 使用方法: check_serial.php?serial=序列号
 * 例如: check_serial.php?serial=1234567890123456
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config.php';

class SerialChecker {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Verification successful! The first time of product verification is in 2025/7/3 18:47:57,and this code has been inquired 2 times.Thank you for supporting Puffmivape
     * 检查序列号是否存在
     * @param string $serial 要检查的序列号
     * @return array 检查结果
     */
    public function checkSerial($serial) {
        try {
            // 验证序列号格式
            $validation = $this->validateSerial($serial);
            if (!$validation['valid']) {
                return $this->formatResponse(false, $validation['message'], [
                    'serial_number' => $serial,
                    'exists' => false,
                    'format_valid' => false
                ]);
            }
            
            // 查询数据库
            $pdo = $this->db->getConnection();
            $sql = "SELECT serial_number, batch_id, created_at, first_queried_at, query_count FROM serial_numbers WHERE serial_number = ? LIMIT 1";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$serial]);
            $result = $stmt->fetch();

            if ($result) {
                // 更新查询记录
                $this->db->updateQueryRecord($serial);

                // 获取更新后的查询统计
                $queryStats = $this->db->getQueryStats($serial);
                $queryCount = $queryStats ? $queryStats['query_count'] : ($result['query_count'] + 1);
                $firstQueriedAt = $queryStats ? $queryStats['first_queried_at'] : null;

                // 如果是首次查询，使用当前时间
                if (!$firstQueriedAt) {
                    $firstQueriedAt = date('Y-m-d H:i:s');
                }

                // 构建验证成功消息
                $message = sprintf(
                    'Verification successful! The first time of product verification is in %s, and this code has been inquired %d times. Thank you for supporting Puffmivape',
                    date('Y/n/j G:i:s', strtotime($firstQueriedAt)),
                    $queryCount
                );

                // 序列号存在
                return $this->formatResponse(1, true, $message, [
                    'serial_number' => $result['serial_number'],
                    'batch_id' => $result['batch_id'],
                    'created_at' => $result['created_at'],
                    'first_queried_at' => $firstQueriedAt,
                    'query_count' => $queryCount,
                    'exists' => true,
                    'format_valid' => true,
                    'database_type' => $this->db->getDbType()
                ]);
            } else {
                // 序列号不存在
                return $this->formatResponse(0,false, 'This code is invalid or wrong. Please double-check your entry or contact our support team for assistance. To ensure your safety, always purchase through regular channels.', [
                    'serial_number' => $serial,
                    'exists' => false,
                    'format_valid' => true,
                    'database_type' => $this->db->getDbType()
                ]);
            }
            
        } catch (Exception $e) {
            return $this->formatResponse(3,false, '查询过程中发生错误: ' . $e->getMessage(), [
                'serial_number' => $serial,
                'exists' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 验证序列号格式
     * @param string $serial 序列号
     * @return array 验证结果
     */
    private function validateSerial($serial) {
        // 检查是否为空
        if (empty($serial)) {
            return [
                'valid' => false,
                'message' => '序列号不能为空'
            ];
        }
        
        // 检查长度
        if (strlen($serial) != 16) {
            return [
                'valid' => false,
                'message' => '序列号长度必须为16位'
            ];
        }
        
        // 检查是否为数字
        if (!is_numeric($serial)) {
            return [
                'valid' => false,
                'message' => '序列号必须为纯数字'
            ];
        }
        
        // 检查前4位是否为0000
        if (substr($serial, 0, 4) === '0000') {
            return [
                'valid' => false,
                'message' => '序列号前4位不能为0000'
            ];
        }
        
        return [
            'valid' => true,
            'message' => '序列号格式正确'
        ];
    }
    
    /**
     * 格式化响应数据
     * @param bool $success 是否成功
     * @param string $message 消息
     * @param array $data 数据
     * @return array 格式化的响应
     */
    private function formatResponse($codeState,$success, $message, $data = []) {
        return [
            'CodeState' => $codeState,
            'success' => $success,
            'code' => 200,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 获取数据库统计信息
     * @return array 统计信息
     */
    public function getStatistics() {
        try {
            $pdo = $this->db->getConnection();

            // 获取总数
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM serial_numbers");
            $stmt->execute();
            $total = $stmt->fetch()['total'];

            // 获取最新记录
            $stmt = $pdo->prepare("SELECT created_at FROM serial_numbers ORDER BY created_at DESC LIMIT 1");
            $stmt->execute();
            $latest = $stmt->fetch();

            // 获取最旧记录
            $stmt = $pdo->prepare("SELECT created_at FROM serial_numbers ORDER BY created_at ASC LIMIT 1");
            $stmt->execute();
            $oldest = $stmt->fetch();

            // 获取查询统计
            $stmt = $pdo->prepare("SELECT COUNT(*) as queried_count FROM serial_numbers WHERE query_count > 0");
            $stmt->execute();
            $queriedCount = $stmt->fetch()['queried_count'];

            $stmt = $pdo->prepare("SELECT SUM(query_count) as total_queries FROM serial_numbers");
            $stmt->execute();
            $totalQueries = $stmt->fetch()['total_queries'] ?: 0;

            $stmt = $pdo->prepare("SELECT MIN(first_queried_at) as first_query, MAX(first_queried_at) as latest_query FROM serial_numbers WHERE first_queried_at IS NOT NULL");
            $stmt->execute();
            $queryTimeRange = $stmt->fetch();

            return [
                'total_serials' => $total,
                'latest_created' => $latest ? $latest['created_at'] : null,
                'oldest_created' => $oldest ? $oldest['created_at'] : null,
                'queried_serials' => $queriedCount,
                'total_queries' => $totalQueries,
                'first_query_time' => $queryTimeRange['first_query'],
                'latest_query_time' => $queryTimeRange['latest_query'],
                'database_type' => $this->db->getDbType()
            ];

        } catch (Exception $e) {
            return [
                'error' => $e->getMessage()
            ];
        }
    }
}

// 主程序逻辑
try {
    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'code' => 405,
            'message' => '只支持GET请求方法',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        exit;
    }
    
    // 创建检查器实例
    $checker = new SerialChecker();
    
    // 获取参数
    $serial = isset($_GET['serial']) ? trim($_GET['serial']) : '';
    $action = isset($_GET['action']) ? trim($_GET['action']) : 'check';
    
    // 根据action执行不同操作
    switch ($action) {
        case 'check':
            // 检查序列号
            if (empty($serial)) {
                $response = [
                    'success' => false,
                    'code' => 200,
                    'message' => '请提供要查询的序列号',
                    'data' => [
                        'usage' => 'check_serial.php?serial=序列号',
                        'example' => 'check_serial.php?serial=1234567890123456'
                    ],
                    'timestamp' => date('Y-m-d H:i:s')
                ];
            } else {
                $response = $checker->checkSerial($serial);
            }
            break;
            
        case 'stats':
            // 获取统计信息
            $stats = $checker->getStatistics();
            $response = [
                'success' => true,
                'code' => 200,
                'message' => '获取统计信息成功',
                'data' => $stats,
                'timestamp' => date('Y-m-d H:i:s')
            ];
            break;
            
        default:
            $response = [
                'success' => false,
                'code' => 200,
                'message' => '未知的操作类型',
                'data' => [
                    'available_actions' => ['check', 'stats'],
                    'usage' => [
                        'check' => 'check_serial.php?serial=序列号',
                        'stats' => 'check_serial.php?action=stats'
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ];
    }
    
    // 记录查询日志
    $logMessage = date('Y-m-d H:i:s') . " - API查询: action={$action}, serial={$serial}, result=" . ($response['success'] ? 'success' : 'failed') . "\n";
    file_put_contents('api.log', $logMessage, FILE_APPEND | LOCK_EX);
    
    // 返回JSON响应
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    // 异常处理
    $response = [
        'success' => false,
        'code' => 200,
        'message' => 'API内部错误: ' . $e->getMessage(),
        'data' => [
            'error_type' => get_class($e),
            'error_line' => $e->getLine(),
            'error_file' => basename($e->getFile())
        ],
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    // 记录错误日志
    $errorMessage = date('Y-m-d H:i:s') . " - API错误: " . $e->getMessage() . "\n";
    file_put_contents('error.log', $errorMessage, FILE_APPEND | LOCK_EX);
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
}
?> 